import React, { useCallback, useMemo, useState } from 'react';
import { filter, get } from 'lodash';
import { useHistory, useRouteMatch, useLocation } from 'react-router-dom';

import useT from '../../../../../../common/components/utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../common/components/dataViews/GqlFullCrudTable';
import eContentItem from '../../../../../../common/data/eContent/eContentItem.graphql';
import eContentItems from '../../../../../../common/data/eContent/eContentItems.graphql';
import eContentItemsCount from '../../../../../../common/data/eContent/eContentItemsCount.graphql';
import createEContentItem from '../../../../../../common/data/eContent/createEContentItem.graphql';
import updateEContentItem from '../../../../../../common/data/eContent/updateEContentItem.graphql';
import deleteEContentItem from '../../../../../../common/data/eContent/deleteEContentItem.graphql';

import FilterBar from '../../../../../../common/components/controls/FilterBar';
import { Active } from '../../../../../../model/StatusWithDraft';
import cookColumns from './tableColumns';
import EContentItemForm from '../../form';
import IEContentResource from '../../../../../../common/abstract/EContent/IEContentResource';
import IEContentItem from '../../../../../../common/abstract/EContent/IEContentItem';
import {
  isEContentLibraryResource,
  TEContentLibraryResourcesTreeTypes,
} from '../../../../../../model/EContentLibraryResourcesTypeNames';
import ILanguage from '../../../../../../common/abstract/ILanguage';
import {
  DefaultPageSize,
  MinPageSize,
} from '../../../../../../common/components/other/PageSizeSelect/pageSizes';
import usePaginationRouting from '../../../../../../common/data/hooks/usePaginationRouting';

export interface IItemsView {
  selectedResources?: Partial<IEContentResource>[];
  libraryId?: number;
  libraryName?: string;
  usedKeywords?: string[];
  isListItemQueryPrevented(variables: object): boolean;
  goToCreate(): void;
  cookQueryVariables: Function;
  rightActionButtons: ((props: object) => JSX.Element) | JSX.Element | null;
  onFilterChange: (filter: object) => void;
  filterKey: string;
  cookFilterModels: (
    items: TEContentLibraryResourcesTreeTypes[],
  ) => TEContentLibraryResourcesTreeTypes[];
  filterLanguages: (options: ILanguage[], values: object) => ILanguage[];
  parentUrl?: string;
}

const ItemsView: React.FC<IItemsView> = ({
  selectedResources,
  libraryId,
  libraryName,
  usedKeywords,
  isListItemQueryPrevented,
  goToCreate,
  cookQueryVariables,
  rightActionButtons,
  onFilterChange,
  filterKey,
  cookFilterModels,
  filterLanguages,
}) => {
  const t = useT();
  const history = useHistory();
  const match = useRouteMatch();
  const location = useLocation();
  const columns = useMemo(() => cookColumns(t, libraryId), [t, libraryId]);
  const [filter, setFilter] = useState();

  const { pageSize: _pageSize, pageNumber: _pageNumber } = usePaginationRouting(
    {
      defaultPageSize: DefaultPageSize,
      defaultPageNumber: 1,
    },
  );

  const handleGoBack = useCallback(() => {
    const count = get(filter, 'count', MinPageSize);
    const page = get(filter, 'first', 0) / count + 1;
    history.push(`${match.url}/page/${page}/size/${count}`);
  }, [filter, history, match]);

  const handleFilterChange = useCallback(
    filter => {
      const first = (_pageNumber - 1) * _pageSize;
      if (_pageNumber && first !== filter.first) {
        filter.first = first;
      }
      setFilter(filter);
      onFilterChange(filter);
    },
    [setFilter, onFilterChange, _pageNumber, _pageSize],
  );
  const customTitle = useMemo(() => {
    if (location.pathname.includes('contents/add')) {
      return t('Add Content');
    } else if (location.pathname.endsWith('items')) {
      return t('Items');
    }
    return undefined;
  }, [location, t]);

  const title = useMemo(
    () =>
      location.pathname.endsWith('add/details') ||
      location.pathname.endsWith('add')
        ? 'Item'
        : t('Items'),
    [location, t],
  );

  return (
    <GqlFullCrudTable<
      IEContentItem,
      {
        libraryId?: number;
        libraryName?: string;
        selectedResources?: Partial<IEContentResource>[];
        goToCreateItem: () => void;
        usedKeywords?: string[];
      }
    >
      hasFilters
      hasPagination
      customTitle={customTitle}
      form={{
        component: EContentItemForm,
        default: {
          status: Active.value,
        },
        props: {
          libraryId,
          libraryName,
          selectedResources,
          // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
          // @ts-ignore
          onGoBack: handleGoBack,
          goToCreateItem: goToCreate,
          usedKeywords,
        },
      }}
      gql={{
        query: eContentItems,
        single: eContentItem,
        count: eContentItemsCount,
        create: createEContentItem,
        update: updateEContentItem,
        delete: deleteEContentItem,
        cookQueryVariables,
        isListItemQueryPrevented,
      }}
      list={{
        hasFormActions: true,
        rightActionButtons,
        filterKey,
        canDelete,
        onFilterChange: handleFilterChange,
        filterComponent: {
          resources: FilterBar.EContentLibraryResourcesTreeSelector,
          languageId: FilterBar.LanguagesMultiSelector,
          status: FilterBar.StatusWithDraftMultiSelector,
        },
        filterComponentProps: {
          resources: {
            libraryId,
            syntheticRootNodeName: libraryName,
            cookModels: cookFilterModels,
            initialStatus: Active.value,
            hasFilter: true,
            isSingleSelector: false,
            selectionFilter,
            initialSelectAll: true,
          },
          languageId: {
            filterOptions: filterLanguages,
            isEmptyAllowed: true,
            filterKey,
          },
          status: {
            hasAllOption: true,
          },
          initialFilter: {
            status: [], // "All" - empty array means all statuses
            languageId: [], // "All" - empty array means all languages
            searchQuery: '',
            resources: [], // Will be auto-populated by initialSelectAll
          },
        },
        tableConfig: {
          columns,
        },
      }}
      title={title}
    />
  );
};

export default ItemsView;

const canDelete = () => false;

const selectionFilter = nodes => filter(nodes, isEContentLibraryResource);
