import React, { useCallback, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { filter, isEmpty } from 'lodash';

import useT from '../../../../../../common/components/utils/Translations/useT';
import GqlFullCrudTable from '../../../../../../common/components/dataViews/GqlFullCrudTable';
import eContentItemContent from '../../../../../../common/data/eContent/eContentItemContent.graphql';
import eContentContentView from '../../../../../../common/data/eContent/eContentContentView.graphql';
import eContentContentViewCount from '../../../../../../common/data/eContent/eContentContentViewCount.graphql';

import FilterBar from '../../../../../../common/components/controls/FilterBar';
import { Active } from '../../../../../../model/StatusWithDraft';
import cookColumns from './tableColumns';
import IEContentResource from '../../../../../../common/abstract/EContent/IEContentResource';
import { IItemsView } from '../ItemsView/ItemsView';
import IEContentContent from '../../../../../../common/abstract/EContent/IEContentContent';
import { isEContentLibraryResource } from '../../../../../../model/EContentLibraryResourcesTypeNames';
import usePaginationRouting from '../../../../../../common/data/hooks/usePaginationRouting';
import { DefaultPageSize } from '../../../../../../common/components/other/PageSizeSelect';

const ContentView: React.FC<IItemsView> = ({
  selectedResources,
  libraryId,
  libraryName,
  isListItemQueryPrevented,
  cookQueryVariables,
  rightActionButtons,
  onFilterChange,
  filterKey,
  cookFilterModels,
  filterLanguages,
  parentUrl,
}) => {
  const t = useT();
  const history = useHistory();

  const { pageSize: _pageSize, pageNumber: _pageNumber } = usePaginationRouting(
    {
      defaultPageSize: DefaultPageSize,
      defaultPageNumber: 1,
    },
  );
  const handleFilterChange = useCallback(
    filter => {
      const first = (_pageNumber - 1) * _pageSize;
      if (_pageNumber && first !== filter.first) {
        filter.first = first;
      }
      onFilterChange(filter);
    },
    [onFilterChange, _pageNumber, _pageSize],
  );
  const columns = useMemo(() => cookColumns(t, parentUrl), [t, parentUrl]);

  const handleRowClick = useCallback(
    ({ id, item }, column) => {
      if (column && column.id === 'name') {
        history.push(`${parentUrl}/edit/${item.id}/contents`, {
          fromPage: 'contentview',
        });
      } else {
        history.push(`${parentUrl}/edit/${item.id}/contents/edit/${id}`, {
          fromPage: 'contentview',
        });
      }
    },
    [history, parentUrl],
  );

  return (
    <GqlFullCrudTable<
      IEContentContent,
      {
        libraryId?: number;
        libraryName?: string;
        selectedResources?: Partial<IEContentResource>[];
        goToCreateItem: () => void;
        usedKeywords?: string[];
      }
    >
      hasFilters
      hasPagination
      gql={{
        query: eContentContentView,
        single: eContentItemContent,
        count: eContentContentViewCount,
        cookQueryVariables,
        isListItemQueryPrevented,
      }}
      list={{
        onRowClick: handleRowClick,
        hasFormActions: true,
        rightActionButtons,
        filterKey,
        canDelete,
        onFilterChange: handleFilterChange,
        filterComponent: {
          resources: FilterBar.EContentLibraryResourcesTreeSelector,
          languageId: FilterBar.LanguagesMultiSelector,
          status: FilterBar.StatusWithDraftMultiSelector,
        },
        filterComponentProps: {
          resources: {
            libraryId,
            syntheticRootNodeName: libraryName,
            cookModels: cookFilterModels,
            initialStatus: Active.value,
            hasFilter: true,
            isSingleSelector: false,
            selectionFilter,
            initialSelectAll: true,
          },
          languageId: {
            filterOptions: filterLanguages,
            isDisabled: isEmpty(selectedResources),
            isEmptyAllowed: true,
            filterKey,
          },
          status: {
            hasAllOption: true,
          },
          initialFilter: {
            status: [], // "All" - empty array means all statuses
            languageId: [], // "All" - empty array means all languages
            searchQuery: '',
            resources: [], // Will be auto-populated by initialSelectAll
          },
        },
        tableConfig: {
          columns,
        },
      }}
      title="Content"
    />
  );
};

export default ContentView;

const canDelete = () => false;

const selectionFilter = nodes => filter(nodes, isEContentLibraryResource);
